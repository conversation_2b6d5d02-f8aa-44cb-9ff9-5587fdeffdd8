# DuckDuckGo 搜索集成

本文档说明如何在 Deep Research 项目中使用 DuckDuckGo 搜索功能。

## 功能特点

✅ **已实现的功能**：
- 类似于 Python `duckduckgo-search` 库的使用方式
- 支持 DuckDuckGo 即时答案 API
- 完整的错误处理和回退机制
- 支持本地和服务端代理模式
- 可配置的搜索参数（地区、安全搜索等）

## 使用方法

### 1. 配置搜索提供商

在项目设置中，将搜索提供商设置为 `duckduckgo`：

```typescript
// 在设置中
searchProvider: "duckduckgo"
```

### 2. 环境变量配置

在 `.env` 文件中添加（可选）：

```bash
# DuckDuckGo API 代理 URL（可选）
DUCKDUCKGO_API_BASE_URL=https://html.duckduckgo.com

# 在 MCP 服务中启用 DuckDuckGo
MCP_SEARCH_PROVIDER=duckduckgo
```

### 3. 程序化使用

```typescript
import { createSearchProvider } from '@/utils/deep-research/search';

// 创建 DuckDuckGo 搜索提供商
const searchOptions = {
  provider: "duckduckgo",
  query: "artificial intelligence",
  maxResult: 5,
  scope: "wt-wt", // 全球地区
};

const results = await createSearchProvider(searchOptions);
console.log(results.sources); // 搜索结果
```

## 搜索实现原理

### 类似 Python 的使用方式

我们的实现模仿了 Python 中 `duckduckgo-search` 库的使用方式：

**Python 版本**：
```python
from duckduckgo_search import DDGS

results = DDGS().text("python programming", max_results=5)
```

**我们的 TypeScript 版本**：
```typescript
const results = await createSearchProvider({
  provider: "duckduckgo",
  query: "python programming", 
  maxResult: 5
});
```

### 搜索流程

1. **即时答案 API**：首先尝试使用 DuckDuckGo 的即时答案 API
2. **结构化结果**：提取摘要、定义和相关主题
3. **错误处理**：如果 API 失败，返回回退结果
4. **结果格式化**：统一格式化为 `Source[]` 类型

### 支持的搜索参数

- `query`: 搜索关键词
- `maxResult`: 最大结果数量（默认 5）
- `region`: 搜索地区（默认 "wt-wt" 全球）
- `safesearch`: 安全搜索级别（"off", "moderate", "strict"）

## API 端点

项目自动创建了以下 API 端点：

- `GET /api/search/duckduckgo/*` - DuckDuckGo 搜索代理
- `POST /api/search/duckduckgo/*` - DuckDuckGo 搜索代理

## 配置选项

### 用户界面设置

在设置页面中，用户可以配置：

- **搜索提供商**：选择 "duckduckgo"
- **DuckDuckGo 地区**：设置搜索地区偏好
- **安全搜索**：设置安全搜索级别
- **API 代理**：自定义 API 代理地址

### 存储配置

```typescript
interface SettingStore {
  searchProvider: string;           // "duckduckgo"
  duckduckgoApiProxy: string;      // 自定义代理地址
  duckduckgoRegion: string;        // 搜索地区 "wt-wt"
  duckduckgoSafesearch: string;    // 安全搜索 "moderate"
}
```

## 与其他搜索引擎的比较

| 特性 | DuckDuckGo | Tavily | SearXNG |
|------|------------|--------|---------|
| 隐私保护 | ✅ 优秀 | ⚠️ 一般 | ✅ 优秀 |
| API 访问 | ✅ 免费 | 💰 付费 | ✅ 免费 |
| 结果质量 | ⚠️ 中等 | ✅ 优秀 | ✅ 优秀 |
| 配置复杂度 | ✅ 简单 | ✅ 简单 | ⚠️ 复杂 |

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 尝试使用代理服务器
   - 检查防火墙设置

2. **搜索结果为空**
   - DuckDuckGo 即时答案 API 对某些查询可能返回空结果
   - 系统会自动提供回退结果

3. **CORS 错误**
   - 在生产环境中使用服务端代理模式
   - 设置正确的 API 代理地址

### 调试模式

启用调试模式查看详细日志：

```typescript
// 在设置中启用调试
debug: "enable"
```

## 未来改进

- [ ] 支持图片搜索
- [ ] 支持新闻搜索
- [ ] 支持视频搜索
- [ ] 改进 HTML 解析（如果需要）
- [ ] 添加缓存机制
- [ ] 支持更多搜索参数

## 总结

DuckDuckGo 搜索集成为 Deep Research 项目提供了一个注重隐私的搜索选项。虽然搜索结果的丰富程度可能不如某些商业搜索 API，但它提供了良好的隐私保护和免费访问。

对于需要更高质量搜索结果的场景，建议结合使用多个搜索提供商或使用 SearXNG 聚合多个搜索引擎的结果。
