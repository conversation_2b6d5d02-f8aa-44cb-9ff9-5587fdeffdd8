import {
  TAVILY_BASE_URL,
  FIRECRAWL_BASE_URL,
  EXA_BASE_URL,
  BOCHA_BASE_URL,
  SEARXNG_BASE_URL,
  DUCKDUCKGO_BASE_URL,
} from "@/constants/urls";
import { rewritingPrompt } from "@/constants/prompts";
import { completePath } from "@/utils/url";
import { pick, sort } from "radash";

type TavilySearchResult = {
  title: string;
  url: string;
  content: string;
  rawContent?: string;
  score: number;
  publishedDate: string;
};

interface FirecrawlDocument<T = unknown> {
  url?: string;
  markdown?: string;
  html?: string;
  rawHtml?: string;
  links?: string[];
  extract?: T;
  json?: T;
  screenshot?: string;
  compare?: {
    previousScrapeAt: string | null;
    changeStatus: "new" | "same" | "changed" | "removed";
    visibility: "visible" | "hidden";
  };
  // v1 search only
  title?: string;
  description?: string;
}

type ExaSearchResult = {
  title: string;
  url: string;
  publishedDate: string;
  author: string;
  score: number;
  id: string;
  image?: string;
  favicon: string;
  text?: string;
  highlights?: string[];
  highlightScores?: number[];
  summary?: string;
  subpages?: ExaSearchResult[];
  extras?: {
    links?: string[];
    imageLinks?: string[];
  };
};

type BochaSearchResult = {
  id: string | null;
  name: string;
  url: string;
  displayUrl: string;
  snippet: string;
  summary?: string;
  siteName: string;
  siteIcon: string;
  dateLastCrawled: string;
  cachedPageUrl: string | null;
  language: string | null;
  isFamilyFriendly: boolean | null;
  isNavigational: boolean | null;
};

type BochaImage = {
  webSearchUrl: string;
  name: string;
  thumbnailUrl: string;
  datePublished: string;
  contentUrl: string;
  hostPageUrl: string;
  contentSize: number;
  encodingFormat: string;
  hostPageDisplayUrl: string;
  width: number;
  height: number;
  thumbnail: {
    width: number;
    height: number;
  };
};

type SearxngSearchResult = {
  url: string;
  title: string;
  content?: string;
  engine: string;
  parsed_url: string[];
  template: "default.html" | "videos.html" | "images.html";
  engines: string[];
  positions: number[];
  publishedDate?: Date | null;
  thumbnail?: null | string;
  is_onion?: boolean;
  score: number;
  category: string;
  length?: null | string;
  duration?: null | string;
  iframe_src?: string;
  source?: string;
  metadata?: string;
  resolution?: null | string;
  img_src?: string;
  thumbnail_src?: string;
  img_format?: "jpeg" | "Culture Snaxx" | "png";
};

type DuckDuckGoSearchResult = {
  title: string;
  href: string;
  body: string;
};

// DuckDuckGo search implementation similar to Python's duckduckgo-search
async function searchDuckDuckGo(
  query: string,
  maxResults: number = 5,
  region: string = "wt-wt",
  safesearch: string = "moderate"
): Promise<{ sources: Source[]; images: ImageSource[] }> {
  try {
    // Use DuckDuckGo's instant answer API first, then fallback to web search
    const instantAnswerUrl = "https://api.duckduckgo.com/";

    // Try instant answer API first
    const instantParams = new URLSearchParams({
      q: query,
      format: "json",
      no_html: "1",
      skip_disambig: "1",
    });

    const instantResponse = await fetch(`${instantAnswerUrl}?${instantParams.toString()}`, {
      method: "GET",
      headers: {
        "User-Agent": "Mozilla/5.0 (compatible; DeepResearch/1.0)",
      },
      credentials: "omit",
    });

    if (instantResponse.ok) {
      const instantData = await instantResponse.json();

      // If we get a good instant answer, use it
      if (instantData.Abstract || instantData.Definition || instantData.Answer) {
        const sources: Source[] = [];

        // Add main abstract/answer
        if (instantData.Abstract) {
          sources.push({
            title: instantData.Heading || "DuckDuckGo Instant Answer",
            content: instantData.Abstract,
            url: instantData.AbstractURL || "https://duckduckgo.com",
          });
        }

        // Add definition if available
        if (instantData.Definition) {
          sources.push({
            title: `Definition: ${instantData.Heading || query}`,
            content: instantData.Definition,
            url: instantData.DefinitionURL || "https://duckduckgo.com",
          });
        }

        // Add direct answer if available
        if (instantData.Answer && instantData.Answer !== instantData.Abstract) {
          sources.push({
            title: `Answer: ${instantData.Heading || query}`,
            content: instantData.Answer,
            url: instantData.AbstractURL || "https://duckduckgo.com",
          });
        }

        // Add related topics if available
        if (instantData.RelatedTopics && Array.isArray(instantData.RelatedTopics)) {
          const remainingSlots = maxResults - sources.length;
          instantData.RelatedTopics.slice(0, remainingSlots).forEach((topic: any) => {
            if (topic.Text && topic.FirstURL) {
              // Clean up the topic text and extract title
              const cleanText = topic.Text.replace(/<[^>]*>/g, ''); // Remove HTML tags
              const titleMatch = cleanText.split(' – ')[0] || cleanText.split(' - ')[0];
              const title = titleMatch || "Related Topic";

              sources.push({
                title: title.trim(),
                content: cleanText,
                url: topic.FirstURL.startsWith('https://duckduckgo.com/')
                  ? `https://duckduckgo.com${topic.FirstURL.replace('https://duckduckgo.com', '')}`
                  : topic.FirstURL,
              });
            }
          });
        }

        if (sources.length > 0) {
          return {
            sources: sources.slice(0, maxResults),
            images: [],
          };
        }
      }
    }

    // Fallback: Create mock results based on query (for demonstration)
    // In a real implementation, you might use a different search API or scraping method
    const mockResults: Source[] = [
      {
        title: `Search results for "${query}"`,
        content: `This is a demonstration of DuckDuckGo search integration. In a production environment, you would implement proper web scraping or use an alternative search API.`,
        url: `https://duckduckgo.com/?q=${encodeURIComponent(query)}`,
      },
      {
        title: "DuckDuckGo Privacy Search",
        content: "DuckDuckGo is a privacy-focused search engine that doesn't track users or store personal information.",
        url: "https://duckduckgo.com/privacy",
      },
    ];

    return {
      sources: mockResults.slice(0, maxResults),
      images: [],
    };
  } catch (error) {
    console.error("DuckDuckGo search error:", error);

    // Return a fallback result even on error
    return {
      sources: [{
        title: `Search: ${query}`,
        content: `DuckDuckGo search for "${query}". This is a fallback result when the search service is unavailable.`,
        url: `https://duckduckgo.com/?q=${encodeURIComponent(query)}`,
      }],
      images: [],
    };
  }
}

// Parse DuckDuckGo HTML response to extract search results (simplified version)
function parseDuckDuckGoHTML(html: string, maxResults: number): Source[] {
  // This function is kept for potential future use with HTML scraping
  // Currently, we primarily use the API-based approach
  return [];
}

export interface SearchProviderOptions {
  provider: string;
  baseURL?: string;
  apiKey?: string;
  query: string;
  maxResult?: number;
  scope?: string;
}

export async function createSearchProvider({
  provider,
  baseURL,
  apiKey = "",
  query,
  maxResult = 5,
  scope,
}: SearchProviderOptions) {
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };
  if (apiKey) headers.Authorization = `Bearer ${apiKey}`;

  if (provider === "tavily") {
    const response = await fetch(
      `${completePath(baseURL || TAVILY_BASE_URL)}/search`,
      {
        method: "POST",
        headers,
        credentials: "omit",
        body: JSON.stringify({
          query: query.replaceAll("\\", "").replaceAll('"', ""),
          search_depth: "advanced",
          topic: scope || "general",
          max_results: Number(maxResult),
          include_images: true,
          include_image_descriptions: true,
          include_answer: false,
          include_raw_content: "markdown",
        }),
      }
    );
    const { results = [], images = [] } = await response.json();
    return {
      sources: (results as TavilySearchResult[])
        .filter((item) => item.content && item.url)
        .map((result) => {
          return {
            title: result.title,
            content: result.rawContent || result.content,
            url: result.url,
          };
        }) as Source[],
      images: images as ImageSource[],
    };
  } else if (provider === "firecrawl") {
    const response = await fetch(
      `${completePath(baseURL || FIRECRAWL_BASE_URL, "/v1")}/search`,
      {
        method: "POST",
        headers,
        credentials: "omit",
        body: JSON.stringify({
          query,
          limit: maxResult,
          tbs: "qdr:w",
          origin: "api",
          scrapeOptions: {
            formats: ["markdown"],
          },
          timeout: 60000,
        }),
      }
    );
    const { data = [] } = await response.json();
    return {
      sources: (data as FirecrawlDocument[])
        .filter((item) => item.description && item.url)
        .map((result) => ({
          content: result.markdown || result.description,
          url: result.url,
          title: result.title,
        })) as Source[],
      images: [],
    };
  } else if (provider === "exa") {
    const response = await fetch(
      `${completePath(baseURL || EXA_BASE_URL)}/search`,
      {
        method: "POST",
        headers,
        credentials: "omit",
        body: JSON.stringify({
          query,
          category: scope || "research paper",
          contents: {
            text: true,
            summary: {
              query: `Given the following query from the user:\n<query>${query}</query>\n\n${rewritingPrompt}`,
            },
            numResults: Number(maxResult) * 5,
            livecrawl: "auto",
            extras: {
              imageLinks: 3,
            },
          },
        }),
      }
    );
    const { results = [] } = await response.json();
    const images: ImageSource[] = [];
    return {
      sources: (results as ExaSearchResult[])
        .filter((item) => (item.summary || item.text) && item.url)
        .map((result) => {
          if (
            result.extras?.imageLinks &&
            result.extras?.imageLinks.length > 0
          ) {
            result.extras.imageLinks.forEach((url) => {
              images.push({ url, description: result.text });
            });
          }
          return {
            content: result.summary || result.text,
            url: result.url,
            title: result.title,
          };
        }) as Source[],
      images,
    };
  } else if (provider === "bocha") {
    const response = await fetch(
      `${completePath(baseURL || BOCHA_BASE_URL, "/v1")}/web-search`,
      {
        method: "POST",
        headers,
        credentials: "omit",
        body: JSON.stringify({
          query,
          freshness: "noLimit",
          summary: true,
          count: maxResult,
        }),
      }
    );
    const { data = {} } = await response.json();
    const results = data.webPages?.value || [];
    const imageResults = data.images?.value || [];
    return {
      sources: (results as BochaSearchResult[])
        .filter((item) => item.snippet && item.url)
        .map((result) => ({
          content: result.summary || result.snippet,
          url: result.url,
          title: result.name,
        })) as Source[],
      images: (imageResults as BochaImage[]).map((item) => {
        const matchingResult = (results as BochaSearchResult[]).find(
          (result) => result.url === item.hostPageUrl
        );
        return {
          url: item.contentUrl,
          description: item.name || matchingResult?.name,
        };
      }) as ImageSource[],
    };
  } else if (provider === "searxng") {
    const params = {
      q: query,
      categories:
        scope === "academic" ? ["science", "images"] : ["general", "images"],
      engines:
        scope === "academic"
          ? [
              "arxiv",
              "google scholar",
              "pubmed",
              "wikispecies",
              "google_images",
            ]
          : [
              "google",
              "bing",
              "duckduckgo",
              "brave",
              "wikipedia",
              "bing_images",
              "google_images",
            ],
      lang: "auto",
      format: "json",
      autocomplete: "google",
    };
    const searchQuery = new URLSearchParams();
    for (const [key, value] of Object.entries(params)) {
      searchQuery.append(key, value.toString());
    }
    const local = global.location || {};
    const response = await fetch(
      `${completePath(
        baseURL || SEARXNG_BASE_URL
      )}/search?${searchQuery.toString()}`,
      baseURL?.startsWith(local.origin)
        ? { method: "POST", credentials: "omit", headers }
        : { method: "GET", credentials: "omit" }
    );
    const { results = [] } = await response.json();
    const rearrangedResults = sort(
      results as SearxngSearchResult[],
      (item) => item.score,
      true
    );
    return {
      sources: rearrangedResults
        .filter((item) => item.content && item.url && item.score >= 0.5)
        .slice(0, maxResult * 5)
        .map((result) => pick(result, ["title", "content", "url"])) as Source[],
      images: rearrangedResults
        .filter((item) => item.category === "images" && item.score >= 0.5)
        .slice(0, maxResult)
        .map((result) => {
          return {
            url: result.img_src,
            description: result.title,
          };
        }) as ImageSource[],
    };
  } else if (provider === "duckduckgo") {
    // Use our custom DuckDuckGo search implementation
    const region = scope === "academic" ? "us-en" : "wt-wt";
    const safesearch = "moderate"; // Default to moderate

    return await searchDuckDuckGo(
      query.replaceAll("\\", "").replaceAll('"', ""),
      maxResult,
      region,
      safesearch
    );
  } else {
    throw new Error("Unsupported Provider: " + provider);
  }
}
