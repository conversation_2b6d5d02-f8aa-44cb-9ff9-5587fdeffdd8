# Deep Research 快速启动指南

## 🚀 Docker 快速启动

### 1. 环境配置

项目已经包含了 `.env` 文件模板。你需要配置至少一个 AI 提供商的 API Key：

```bash
# 编辑 .env 文件
nano .env

# 添加你的 API Key（选择其中一个即可）
GOOGLE_GENERATIVE_AI_API_KEY=your_gemini_api_key_here
# 或者
OPENAI_API_KEY=your_openai_api_key_here
# 或者
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

### 2. 启动服务

```bash
# 构建并启动容器
docker-compose up --build

# 或者在后台运行
docker-compose up -d --build
```

### 3. 访问应用

打开浏览器访问：http://localhost:3333

## 🔧 DuckDuckGo 搜索配置

### 启用 DuckDuckGo 搜索

1. **在 .env 文件中配置**（可选）：
```bash
# 启用 DuckDuckGo 作为默认搜索提供商
MCP_SEARCH_PROVIDER=duckduckgo

# 自定义 DuckDuckGo API 地址（可选）
DUCKDUCKGO_API_BASE_URL=https://html.duckduckgo.com
```

2. **在 Web 界面中配置**：
   - 打开设置页面
   - 搜索提供商选择 "duckduckgo"
   - 配置搜索地区和安全搜索级别

### DuckDuckGo 搜索特点

✅ **优势**：
- 完全免费，无需 API Key
- 注重隐私保护，不跟踪用户
- 类似 Python `duckduckgo-search` 的使用体验
- 支持即时答案和相关主题

⚠️ **注意事项**：
- 搜索结果可能不如商业搜索 API 丰富
- 某些查询可能返回有限的结果
- 建议与其他搜索提供商结合使用

## 📋 常用命令

```bash
# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重新构建
docker-compose build --no-cache

# 进入容器
docker-compose exec deep-research sh
```

## 🛠️ 故障排除

### 1. 端口冲突
如果 3333 端口被占用，修改 `docker-compose.yml`：
```yaml
ports:
  - "3334:3000"  # 改为其他端口
```

### 2. API Key 配置
确保在 `.env` 文件中正确配置了至少一个 AI 提供商的 API Key。

### 3. 网络问题
如果无法访问某些 API，可以配置代理：
```bash
# 在 .env 中添加代理配置
GOOGLE_GENERATIVE_AI_API_BASE_URL=your_proxy_url
OPENAI_API_BASE_URL=your_proxy_url
```

### 4. DuckDuckGo 搜索问题
如果 DuckDuckGo 搜索不工作：
- 检查网络连接
- 尝试使用其他搜索提供商（如 searxng）
- 查看容器日志：`docker-compose logs deep-research`

## 🔄 开发模式

如果你想进行开发：

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问 http://localhost:3000
```

## 📚 更多信息

- [DuckDuckGo 集成文档](./DUCKDUCKGO_INTEGRATION.md)
- [项目主要文档](./README.md)
- [环境变量配置](./env.tpl)

## 🎯 快速测试

启动后，你可以尝试以下测试：

1. **基础搜索测试**：
   - 搜索主题："人工智能的发展历史"
   - 选择 DuckDuckGo 作为搜索提供商
   - 观察搜索结果和报告生成

2. **多语言测试**：
   - 尝试中文、英文等不同语言的查询
   - 测试搜索结果的质量

3. **对比测试**：
   - 同一个查询使用不同的搜索提供商
   - 比较结果的差异

现在你可以开始使用 Deep Research 进行深度研究了！🎉
